package com.ifallious.utils;

import com.ifallious.features.raids.*;
import com.ifallious.features.rmt.*;
import com.ifallious.features.spells.*;
import com.ifallious.features.war.*;
import com.ifallious.utils.config.ConfigManager;
import com.ifallious.utils.Updater;

/**
 * Initializes all mod components
 */
public class Initializer {
    /**
     * Initialize all mod components and features
     * Loads configuration and instantiates all feature classes
     */
    public static void init() {

        ConfigManager.loadConfig();

        new Spellmacro();
        new AuraDodge();
        new AutoRadiance();
        new DeliriousGas();
        new TotemCast();
        new RareMobTracker();
        new CorruptedReminder();
        new Tick();
        new StrongerCharge();
        new TrapUtils();
        new TrapTimer();
        new TCCExitLocator();
        FabricRenderHandler.initialize();

        Updater.checkForUpdates();

    }
}
