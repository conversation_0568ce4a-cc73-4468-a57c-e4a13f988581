package com.ifallious.utils;
import gg.essential.universal.UMinecraft;
import net.fabricmc.fabric.api.client.rendering.v1.HudRenderCallback;
import net.fabricmc.fabric.api.client.rendering.v1.WorldRenderEvents;
import net.minecraft.client.render.BufferBuilder;
import net.minecraft.client.render.Tessellator;
import org.joml.Matrix4f;

public class FabricRenderHandler {
    public static void initialize() {
        TextRenderer3D.initialize();
        // Try this stage instead
        WorldRenderEvents.BEFORE_DEBUG_RENDER.register((context) -> {
        });
        HudRenderCallback.EVENT.register((context, tickDelta) -> {
            TextRenderer3D.renderAll(context);
        });
        // Or try this one if the above doesn't work
        // WorldRenderEvents.END.register((context) -> {
        //     TextRenderer3D.renderAll();
        // });
    }
}
