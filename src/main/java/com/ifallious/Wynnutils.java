package com.ifallious;

import com.ifallious.features.lootrun.LootrunAverage;
import com.ifallious.features.spells.Spellmacro;
import com.ifallious.gui.GUI;
import com.ifallious.utils.ErrorReporter;
import com.ifallious.utils.Initializer;
import com.ifallious.utils.TextRenderer3D;
import com.ifallious.utils.Utils;
import com.ifallious.utils.config.ConfigFeatures;
import com.ifallious.utils.config.ConfigManager;
import com.ifallious.utils.config.ConfigSettings;
import com.ifallious.utils.config.FeatureConfig;
import com.mojang.brigadier.arguments.BoolArgumentType;
import com.mojang.brigadier.arguments.DoubleArgumentType;
import com.mojang.brigadier.arguments.StringArgumentType;
import gg.essential.universal.UChat;
import gg.essential.universal.UPacket;
import gg.essential.universal.wrappers.message.UTextComponent;
import net.fabricmc.api.ModInitializer;
import net.fabricmc.loader.api.FabricLoader;
import net.fabricmc.loader.api.metadata.ModMetadata;
import net.minecraft.client.MinecraftClient;
import net.fabricmc.fabric.api.client.command.v2.*;
import org.apache.commons.lang3.exception.ExceptionUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.Arrays;


public class Wynnutils implements ModInitializer {
	public static final String MOD_ID = "wynnutils";
	public static final ModMetadata MOD_META;
	public static final String NAME;
	public static final String VERSION;
	public static final File FOLDER = FabricLoader.getInstance().getGameDir().resolve(MOD_ID).toFile();
	public static MinecraftClient mc;
	public static final Logger LOGGER = LoggerFactory.getLogger(MOD_ID);
	static {
		MOD_META = FabricLoader.getInstance().getModContainer(MOD_ID).orElseThrow().getMetadata();
		NAME = MOD_META.getName();
		VERSION = MOD_META.getVersion().getFriendlyString();
	}
	@Override
	public void onInitialize() {
		LOGGER.info("Initializing Client");
		mc = MinecraftClient.getInstance();
		if (!FOLDER.exists()) {
			FOLDER.getParentFile().mkdirs();
			FOLDER.mkdir();
		}
		Initializer.init();
		registerCommand();
		
		// Add shutdown hook for ErrorReporter cleanup
		Runtime.getRuntime().addShutdownHook(new Thread(() -> {
			LOGGER.info("Shutting down ErrorReporter");
			ErrorReporter.shutdown();
		}));

		File modsDir = new File(mc.runDirectory, "mods");
		createWynntilsRenameScript(modsDir);
		Runtime.getRuntime().addShutdownHook(new Thread(() -> {
			LOGGER.info("Running Wynntils jar renaming script");
			runWynntilsRenameScript(modsDir);
		}));
	}
	public void registerCommand() {
		ClientCommandRegistrationCallback.EVENT.register((dispatcher, registryAccess) -> {
			dispatcher.register(ClientCommandManager.literal("wynnutils").executes(context -> {
				UChat.chat("Wynnutils " + VERSION);
								String textId = TextRenderer3D.addText("waypoint1", "Waypoint 1", mc.player.getX(), 64.0f, 200.3f);
				return 1;
			})
					.then(ClientCommandManager.literal("spellcycle").executes(context -> {
						GUI.openSpellGUI();
						return 1;
					})
							.then(ClientCommandManager.literal("switch").then(ClientCommandManager.argument("Cycle", StringArgumentType.string()).executes(context -> {
								Spellmacro.setCycle(StringArgumentType.getString(context, "Cycle"));
								return 1;
							})))
							.then(ClientCommandManager.literal("list").executes(context -> {
								UChat.chat("Available cycles: " + Arrays.toString(Spellmacro.getCycleNames()));
								return 1;
							}))
							.then(ClientCommandManager.literal("load").executes(context -> {
								Spellmacro.loadCycles();
								return 1;
							}))
					)
					.then(ClientCommandManager.literal("config").executes(context -> {
						GUI.openConfig();
						return 1;
					})
							.then(ClientCommandManager.literal("list").executes(context -> {
								listFeatures();
								return 1;
							}))
							.then(ClientCommandManager.literal("get").executes(context -> {
								UChat.chat("Please specify a feature name");
								return 1;
							})
									.then(ClientCommandManager.argument("feature", StringArgumentType.string()).executes(context -> {
										String feature = StringArgumentType.getString(context, "feature");
										Boolean value = ConfigManager.getFeature(feature);
										if (value != null) {
											UChat.chat("Feature '" + feature + "' is " + (value ? "enabled" : "disabled"));
										} else {
											UChat.chat("Feature '" + feature + "' not found");
										}
										return 1;
									}))
							)
							.then(ClientCommandManager.literal("set").executes(context -> {
								UChat.chat("Please specify a feature name and value");
								return 1;
							})
									.then(ClientCommandManager.argument("feature", StringArgumentType.string())
											.then(ClientCommandManager.argument("value", BoolArgumentType.bool()).executes(context -> {
												String feature = StringArgumentType.getString(context, "feature");
												boolean value = BoolArgumentType.getBool(context, "value");
												if (ConfigManager.setFeature(feature, value)) {
													UChat.chat("Feature '" + feature + "' set to " + (value ? "enabled" : "disabled"));
												} else {
													UChat.chat("Failed to set feature '" + feature + "'");
												}
												return 1;
											})))
							)
							.then(ClientCommandManager.literal("toggle").executes(context -> {
								UChat.chat("Please specify a feature name");
								return 1;
							})
									.then(ClientCommandManager.argument("feature", StringArgumentType.string()).executes(context -> {
										String feature = StringArgumentType.getString(context, "feature");
										boolean newValue = FeatureConfig.toggle(feature);
										UChat.chat("Feature '" + feature + "' toggled to " + (newValue ? "enabled" : "disabled"));
										return 1;
									}))
							)
							.then(ClientCommandManager.literal("settings").executes(context -> {
								listSettings();
								return 1;
							})
									.then(ClientCommandManager.literal("get").executes(context -> {
										UChat.chat("Please specify a setting name");
										return 1;
									})
											.then(ClientCommandManager.argument("setting", StringArgumentType.string()).executes(context -> {
												String setting = StringArgumentType.getString(context, "setting");
												Double value = ConfigManager.getSetting(setting);
												if (value != null) {
													UChat.chat("Setting '" + setting + "' is " + value);
												} else {
													UChat.chat("Setting '" + setting + "' not found");
												}
												return 1;
											}))
									)
									.then(ClientCommandManager.literal("set").executes(context -> {
										UChat.chat("Please specify a setting name and value");
										return 1;
									})
											.then(ClientCommandManager.argument("setting", StringArgumentType.string())
													.then(ClientCommandManager.argument("value", DoubleArgumentType.doubleArg()).executes(context -> {
														String setting = StringArgumentType.getString(context, "setting");
														double value = DoubleArgumentType.getDouble(context, "value");
														if (ConfigManager.setSetting(setting, value)) {
															UChat.chat("Setting '" + setting + "' set to " + value);
														} else {
															UChat.chat("Failed to set setting '" + setting + "'");
														}
														return 1;
													})))
									)
							)
					).then(ClientCommandManager.literal("lootrunavg").executes(context -> {
						LootrunAverage.getLootrunAvg();
						return 1;
					})
					).then(ClientCommandManager.literal("edit").executes(context -> {
						GUI.openEditorGUI();
						return 1;
					})
					)
			);
		});
	}

	private void listFeatures() {
		UChat.chat("Available features:");
		try {
			ConfigFeatures features = ConfigManager.getConfig().getFeatures();
			for (Field field : ConfigFeatures.class.getDeclaredFields()) {
				field.setAccessible(true);
				String name = field.getName();
				Boolean value = (Boolean) field.get(features);
				UChat.chat(" - " + name + ": " + (value ? "enabled" : "disabled"));
			}
		} catch (Exception e) {
			UChat.chat("Error listing features: " + e.getMessage());
			LOGGER.error("Error listing features", e);
			ErrorReporter.reportError("Error listing features", e.getMessage() + ExceptionUtils.getStackTrace(e));
		}
	}

	private void listSettings() {
		UChat.chat("Available settings:");
		try {
			ConfigSettings settings = ConfigManager.getConfig().getSettings();
			for (Field field : ConfigSettings.class.getDeclaredFields()) {
				field.setAccessible(true);
				String name = field.getName();
				Double value = (Double) field.get(settings);
				UChat.chat(" - " + name + ": " + value);
			}
		} catch (Exception e) {
			UChat.chat("Error listing settings: " + e.getMessage());
			LOGGER.error("Error listing settings", e);
			ErrorReporter.reportError("Error listing settings", e.getMessage() + ExceptionUtils.getStackTrace(e));
		}
	}

	/**
	 * Writes a script to rename Wynntils jar files by removing .disabled extension
	 * This is used when wynnutils.shaded is true to re-enable Wynntils after shutdown
	 */
	private static void createWynntilsRenameScript(File modsDir) {
		try {
			if (!modsDir.exists()) {
				LOGGER.warn("Mods directory not found, skipping Wynntils jar renaming script creation");
				return;
			}

			String os = System.getProperty("os.name").toLowerCase();
			File scriptFile;
			String scriptContent;

			if (os.contains("win")) {
				// Windows batch file
				scriptFile = new File(modsDir, "wynntils_rename.bat");
				scriptContent =
						"@echo off\r\n" +
						"echo Waiting 10 seconds before renaming Wynntils jars...\r\n" +
						"timeout /t 10 >nul\r\n" +
						"cd /d \"" + modsDir.getAbsolutePath() + "\"\r\n" +
						"for %%f in (wynntils-*.jar.disabled) do (\r\n" +
						"    if exist \"%%f\" (\r\n" +
						"        echo Renaming %%f to %%~nf\r\n" +
						"        ren \"%%f\" \"%%~nf\"\r\n" +
						"    )\r\n" +
						")\r\n" +
						"echo Wynntils jar renaming complete\r\n" +
						"del \"%~f0\"\r\n" +
						"exit\r\n";
			} else {
				// Unix shell script
				scriptFile = new File(modsDir, "wynntils_rename.sh");
				scriptContent =
						"#!/bin/sh\n" +
						"echo \"Waiting 10 seconds before renaming Wynntils jars...\"\n" +
						"sleep 10\n" +
						"cd \"" + modsDir.getAbsolutePath() + "\"\n" +
						"for file in wynntils-*.jar.disabled; do\n" +
						"    if [ -f \"$file\" ]; then\n" +
						"        newname=\"${file%.disabled}\"\n" +
						"        echo \"Renaming $file to $newname\"\n" +
						"        mv \"$file\" \"$newname\"\n" +
						"    fi\n" +
						"done\n" +
						"echo \"Wynntils jar renaming complete\"\n" +
						"rm -- \"$0\"\n";
			}

			// Write the script file
			try (FileWriter fw = new FileWriter(scriptFile)) {
				fw.write(scriptContent);
			}

			if (!os.contains("win")) {
				// Make shell script executable
				scriptFile.setExecutable(true);
			}

			LOGGER.info("Wynntils jar renaming script created: " + scriptFile.getAbsolutePath());

		} catch (IOException e) {
			LOGGER.error("Failed to create Wynntils jar renaming script", e);
			ErrorReporter.reportError("Failed to create Wynntils jar renaming script", e.getMessage() + ExceptionUtils.getStackTrace(e));
		} catch (Exception e) {
			LOGGER.error("Unexpected error in Wynntils jar renaming script creation", e);
			ErrorReporter.reportError("Unexpected error in Wynntils jar renaming script creation", e.getMessage() + ExceptionUtils.getStackTrace(e));
		}
	}

	/**
	 * Runs the Wynntils jar renaming script created in the mods directory
	 */
	private static void runWynntilsRenameScript(File modsDir) {
		try {
			String os = System.getProperty("os.name").toLowerCase();
			File scriptFile = os.contains("win")
				? new File(modsDir, "wynntils_rename.bat")
				: new File(modsDir, "wynntils_rename.sh");

			if (!scriptFile.exists()) {
				LOGGER.warn("Wynntils jar renaming script not found: " + scriptFile.getAbsolutePath());
				return;
			}

			if (os.contains("win")) {
				new ProcessBuilder("cmd", "/c", "start", "", "/b", scriptFile.getAbsolutePath()).start();
				LOGGER.info("Would start Wynntils jar renaming script: " + scriptFile.getAbsolutePath());
			} else {
				new ProcessBuilder("sh", scriptFile.getAbsolutePath()).start();
				LOGGER.info("Started Wynntils jar renaming script: " + scriptFile.getAbsolutePath());
			}
		} catch (IOException e) {
			LOGGER.error("Failed to run Wynntils jar renaming script", e);
			ErrorReporter.reportError("Failed to run Wynntils jar renaming script", e.getMessage() + ExceptionUtils.getStackTrace(e));
		} catch (Exception e) {
			LOGGER.error("Unexpected error running Wynntils jar renaming script", e);
			ErrorReporter.reportError("Unexpected error running Wynntils jar renaming script", e.getMessage() + ExceptionUtils.getStackTrace(e));
		}
	}
}